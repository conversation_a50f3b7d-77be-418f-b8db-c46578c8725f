package database

import (
	"github.com/samber/do"
	"github.com/uptrace/bun"
)

// GetDB is a helper function to retrieve the database instance from the DI container
func GetDB(injector *do.Injector) (*bun.DB, error) {
	return do.Invoke[*bun.DB](injector)
}

// MustGetDB is a helper function that panics if the database cannot be retrieved
func MustGetDB(injector *do.Injector) *bun.DB {
	db, err := GetDB(injector)
	if err != nil {
		panic("failed to get database instance: " + err.<PERSON>rror())
	}
	return db
}
